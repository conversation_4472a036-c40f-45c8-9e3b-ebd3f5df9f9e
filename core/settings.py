"""
Django settings for core project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
from dotenv import load_dotenv
import dj_database_url
import smtplib
from email.mime.text import MIMEText
from django.core.mail import send_mail

# Load environment variables from .env file
load_dotenv(override=True)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-$7t0_gqn39qmvpnoh$#ij^s(1j!ambn69823602b4w2#t4df8q')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'

# Set ALLOWED_HOSTS based on DEBUG setting
# In development (DEBUG=True), allow localhost and 127.0.0.1
# In production (DEBUG=False), use values from environment variable
if DEBUG:
    ALLOWED_HOSTS = ['localhost', '127.0.0.1']
else:
    # Parse ALLOWED_HOSTS from comma-separated string to list
    ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', '').split(',') if os.getenv('ALLOWED_HOSTS') else []

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'dcr.apps.DcrConfig',
    'storages',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'core.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'dcr.context_processors.company_info',
            ],
        },
    },
]

WSGI_APPLICATION = 'core.wsgi.application'

# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# 📦 Developer Comment:
# Configures the database based on LOCAL_DB environment variable.
# Uses SQLite for local development, and connects to a production Postgres DB using DATABASE_URL otherwise.




try:
    # Check LOCAL_DB environment variable in .env instead of DEBUG
    # If LOCAL_DB is not set or set to False, defaults to False (production mode).
    USE_LOCAL_DB = os.getenv('LOCAL_DB', 'False').lower() == 'true'
    # If LOCAL_DB is True, use SQLite
    if USE_LOCAL_DB:
        #  Local development uses SQLite
        DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': BASE_DIR / 'db.sqlite3',
            }
        }
        print("Connected to Local Developer Database (SQLite)")
    # If LOCAL_DB is False, use Postgres
    else:
        # 🌐 Production uses PostgreSQL via DATABASE_URL
        DATABASE_URL = os.getenv("DATABASE_URL")
        if not DATABASE_URL:
            raise ValueError("DATABASE_URL environment variable is missing")

        DATABASES = {
            'default': dj_database_url.parse(
                DATABASE_URL,
                conn_max_age=600,
                ssl_require=True
            )
        }
        print("Connected to Production Database (Postgres)")

except Exception as e:
    # Error handling remains unchanged
    error_message = ("Unable to connect to the Production Database (Postgres) via the configured database connection "
                     "in the env file. Contact Admin to review the database connection configuration.")
    print(error_message)
    DATABASE_CONNECTION_ERROR = str(e)
    DATABASES = {}  # Optional: Clear DB config to prevent migrations from failing

    # Get admin and contact emails from environment variables
    admin_email = os.environ.get('ADMIN_EMAIL')
    contact_email = os.environ.get('CONTACT_EMAIL')

    # Send email notification about database connection failure
    if admin_email and contact_email:
        try:
            # Get email configuration from environment variables
            email_host = os.environ.get('EMAIL_HOST')
            email_port = int(os.environ.get('EMAIL_PORT', 587))
            email_host_user = os.environ.get('EMAIL_HOST_USER')
            email_host_password = os.environ.get('EMAIL_HOST_PASSWORD')
            email_use_tls = os.environ.get('EMAIL_USE_TLS', 'True') == 'True'
            from_email = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

            print(f"Email configuration:")
            print(f"EMAIL_HOST: {email_host}")
            print(f"EMAIL_PORT: {email_port}")
            print(f"EMAIL_HOST_USER: {email_host_user}")
            print(f"EMAIL_USE_TLS: {email_use_tls}")
            print(f"DEFAULT_FROM_EMAIL: {from_email}")
            print(f"ADMIN_EMAIL: {admin_email}")
            print(f"CONTACT_EMAIL: {contact_email}")

            if email_host and email_host_user and email_host_password:
                # Create message
                msg = MIMEText(f"{error_message}\n\nError details: {str(e)}")
                msg['Subject'] = "URGENT: Database Connection Failure"
                msg['From'] = from_email
                msg['To'] = ', '.join([admin_email, contact_email])

                # Send email
                print(f"Attempting to send email to: {admin_email}, {contact_email}")
                print(f"Using SMTP server: {email_host}:{email_port}")

                # Enable debug output for SMTP connection
                server = smtplib.SMTP(email_host, email_port)
                server.set_debuglevel(1)

                if email_use_tls:
                    print("Starting TLS")
                    server.starttls()

                print(f"Logging in with user: {email_host_user}")
                server.login(email_host_user, email_host_password)

                print("Sending message")
                server.send_message(msg)

                print("Quitting server")
                server.quit()

                print("Email notification sent to administrators")
            else:
                print("Email configuration incomplete. Could not send notification.")
        except Exception as email_error:
            print(f"Failed to send email notification: {str(email_error)}")
            print(f"Error type: {type(email_error).__name__}")
            print(f"Error details: {str(email_error)}")

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'America/New_York'
USE_I18N = True  # Enables translation of content
USE_TZ = True  # Enables timezone-aware datetimes

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

# Check if we should use Digital Ocean Spaces for storage
USE_DO_STORAGES = os.getenv('USE_DO_STORAGES', 'False') == 'True'
print(f"USE_DO_STORAGES environment variable: {os.getenv('USE_DO_STORAGES')}")
print(f"USE_DO_STORAGES setting: {USE_DO_STORAGES}")

if USE_DO_STORAGES:
    # Digital Ocean Spaces configuration
    print("Using Digital Ocean Spaces for static and media files")

    # Set STATIC_ROOT even when using DO Spaces (required by Django)
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

    # Tell Django where to look for static files
    STATICFILES_DIRS = [
        os.path.join(BASE_DIR, 'static'),
    ]

    # AWS S3 settings (used by django-storages for DO Spaces)
    AWS_ACCESS_KEY_ID = os.getenv('SPACES_KEY')
    AWS_SECRET_ACCESS_KEY = os.getenv('SPACES_SECRET')
    AWS_STORAGE_BUCKET_NAME = os.getenv('SPACES_BUCKET')
    AWS_S3_ENDPOINT_URL = os.getenv('SPACES_ENDPOINT')
    AWS_S3_REGION_NAME = os.getenv('SPACES_REGION_NAME')
    AWS_DEFAULT_ACL = 'public-read'
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',
    }
    AWS_LOCATION = 'static'
    AWS_QUERYSTRING_AUTH = False

    # Storage backends 
    # STATICFILES_STORAGE = 'core.custom_backend.StaticStorage'
    # DEFAULT_FILE_STORAGE = 'core.custom_backend.MediaStorage'

    # Use the new STORAGES setting for Django 5.1+
    STORAGES = {
        "default": {
            "BACKEND": "core.custom_backend.MediaStorage",
        },
        "staticfiles": {
            "BACKEND": "core.custom_backend.StaticStorage",
        },
    }

    # URLs
    STATIC_URL = f'{AWS_S3_ENDPOINT_URL}/{AWS_STORAGE_BUCKET_NAME}/static/'
    MEDIA_URL = f'{AWS_S3_ENDPOINT_URL}/{AWS_STORAGE_BUCKET_NAME}/media/'

else:
    # Local storage configuration
    print("Using Local Storage for static and media files")

    STATIC_URL = 'static/'
    STATICFILES_DIRS = [
        os.path.join(BASE_DIR, 'static'),
    ]
    STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

    # Media files (User uploaded files)
    MEDIA_URL = '/media/'
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

    # Use the new STORAGES setting for Django 5.1+
    STORAGES = {
        "default": {
            "BACKEND": "django.core.files.storage.FileSystemStorage",
            "OPTIONS": {
                "location": MEDIA_ROOT,
                "base_url": MEDIA_URL,
            },
        },
        "staticfiles": {
            "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
            "OPTIONS": {
                "location": STATIC_ROOT,
                "base_url": STATIC_URL,
            },
        },
    }

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.dynu.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True') == 'True'
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', 'Deer Creek Rentals <<EMAIL>>')
CONTACT_EMAIL = os.environ.get('CONTACT_EMAIL', '<EMAIL>')
